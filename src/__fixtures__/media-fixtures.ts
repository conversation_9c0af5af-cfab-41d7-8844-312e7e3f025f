import type { UploadedMediaFile } from '@g17eco/types/insight-custom-dashboard';
import { generateObjectId } from '@utils/object-id';

// Mock file fixtures
export const createMockImageFile = (overrides?: Partial<UploadedMediaFile>): UploadedMediaFile => ({
  name: 'test-image.jpg',
  url: 'https://example.com/image.jpg',
  type: 'image/jpeg',
  documentId: generateObjectId(),
  ...overrides,
});

export const createMockVideoFile = (overrides?: Partial<UploadedMediaFile>): UploadedMediaFile => ({
  name: 'test-video.mp4',
  url: 'https://example.com/video.mp4',
  type: 'video/mp4',
  documentId: generateObjectId(),
  ...overrides,
});
