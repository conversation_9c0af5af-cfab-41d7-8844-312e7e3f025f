import { InitiativeDocument } from '../types/document';
import { EFileType, fileTypeIcon, getIconClass, readFilePromise, removeFileFromArray, triggerDownload } from './files';
import { ExistingEvidenceFile, QuestionSubmitFormData } from '@g17eco/types/questionInterfaces';
import Axios from 'axios';
import { describe, vi } from 'vitest';

describe('Axios.toFormData', () => {
  const evidence = {
    _id: '123',
    metadata: {
      name: 'meta name',
      mimetype: 'meta type',
      extension: '.meta',
    },
    public: false,
    url: '/',
    path: '/',
    size: 1024,
    userId: 'userId',
    created: new Date(),
    status: 'updated',
    type: 'link',
  } as ExistingEvidenceFile;
  const testArray: any = [{ name: 'name' }, 123, '123', [123]];
  const inputObj: QuestionSubmitFormData & { array: Array<any> } = {
    autoVerify: 'true',
    existingEvidence: [evidence],
    files: [
      new File(['sample'], 'sample.txt', {
        type: 'text/plain',
      }),
    ],
    array: testArray,
  };

  describe('when have input data then formData should serialize correctly', () => {
    const formData: FormData = Axios.toFormData(inputObj, new FormData()) as FormData;

    it('should have formData for autoVerify, existingEvidence, files and array', () => {
      expect(formData.get('autoVerify')).toEqual(inputObj.autoVerify);
      expect(formData.get('existingEvidence[0][_id]')).toEqual(evidence._id);
      expect(formData.get('existingEvidence[0][metadata][name]')).toEqual(evidence.metadata.name);
      expect(formData.get('existingEvidence[0][metadata][mimetype]')).toEqual(evidence.metadata.mimetype);
      expect(formData.get('existingEvidence[0][metadata][extension]')).toEqual(evidence.metadata.extension);
      expect(formData.get('existingEvidence[0][public]')).toEqual(String(evidence.public));
      expect(formData.get('existingEvidence[0][url]')).toEqual(evidence.url);
      expect(formData.get('existingEvidence[0][path]')).toEqual(evidence.path);
      expect(formData.get('existingEvidence[0][size]')).toEqual(String(evidence.size));
      expect(formData.get('existingEvidence[0][userId]')).toEqual(evidence.userId);
      expect(formData.get('existingEvidence[0][status]')).toEqual(evidence.status);
      expect(formData.get('existingEvidence[0][type]')).toEqual(evidence.type);
      expect(formData.has('files[]')).toBeTruthy();
      expect(formData.get('array[0][name]')).toEqual(testArray[0].name);
      expect(formData.get('array[1]')).toEqual(String(testArray[1]));
      expect(formData.get('array[2]')).toEqual(testArray[2]);
      expect(formData.get('array[3][0]')).toEqual(String(testArray[3][0]));
    });
  });
});

describe('files.getIconClass', () => {
  const testcases = [
    [EFileType.video, fileTypeIcon.video],
    [EFileType.image, fileTypeIcon.image],
    [EFileType.pdf, fileTypeIcon['application/pdf']],
  ];
  it(`When file does not have metadata then should return ${fileTypeIcon.default}`, () => {
    const result = getIconClass(undefined);
    expect(result).toEqual(fileTypeIcon.default);
  });
  test.each(testcases)('When file has metadata: %s then should return %s', (type, icon) => {
    const result = getIconClass({
      metadata: {
        mimetype: type,
      },
    } as InitiativeDocument);
    expect(result).toEqual(icon);
  });
});

describe('files.triggerDownload', () => {
  afterEach(() => {
    vi.resetAllMocks();
  });

  it('when document is null then should return undefined', () => {
    const result = triggerDownload();
    expect(result).toBeUndefined();
  });
  it('when url is undefined then should return undefined', () => {
    const result = triggerDownload({ url: undefined });
    expect(result).toBeUndefined();
  });
  it('when url has value then should trigger window open', () => {
    window.open = vi.fn();
    triggerDownload({ url: 'test.com' });
    expect(window.open).toBeCalledWith('test.com', '_blank', '');
  });
});

describe('readFilePromise', () => {
  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should return string data when FileReader loads', async () => {
    const mockBlob = new Blob([new ArrayBuffer(8)], { type: 'application/octet-stream' });
    global.fetch = vi.fn().mockResolvedValue({
      blob: vi.fn().mockResolvedValue(mockBlob),
    });

    const result = await readFilePromise('path/to/file');
    expect(result).eq('data:application/octet-stream;base64,AAAAAAAAAAA=');
  });

  it('should log the error and reject when fetch fails', async () => {
    global.fetch = vi.fn().mockRejectedValue(new Error('Fetch error'));
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    await expect(readFilePromise('path/to/file')).rejects.toThrow('Fetch error');
    expect(consoleErrorSpy).toHaveBeenCalled();
  });

  it('should log the error and reject when FileReader fails', async () => {
    const buffer = new ArrayBuffer(8);
    const uint8Array = new Uint8Array(buffer);
    uint8Array.set([72, 101, 108, 108, 111, 32, 87, 111]);
    const mockBlob = new Blob([buffer], { type: 'application/octet-stream' });
    global.fetch = vi.fn().mockResolvedValue({
      blob: vi.fn().mockResolvedValue(mockBlob),
    });

    vi.spyOn(global, 'FileReader').mockImplementation(function () {
      return {
        readAsDataURL: function (this: FileReader) {
          this.onload?.({
            loaded: 8,
            lengthComputable: true,
            total: 8,
            target: { result: null },
          } as ProgressEvent<FileReader>);
        },
      } as unknown as FileReader;
    });

    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    await expect(readFilePromise('path/to/file')).rejects.toThrow('Failed to fetch remote resource');
    expect(consoleErrorSpy).toHaveBeenCalled();
  });
});

describe('removeFileFromArray', () => {
  const mockRevokeObjectURL = vi.fn();
  beforeEach(() => {
    global.URL.revokeObjectURL = mockRevokeObjectURL;
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should remove file from array and clean up URL', () => {
    const file1 = {
      url: 'blob:http://localhost/file1-url',
    };

    const file2 = {
      url: 'blob:http://localhost/file2-url',
      ratio: 1.2,
    };

    const file3 = {
      url: 'blob:http://localhost/file3-url',
    };

    const files = [file1, file2, file3];

    const updatedFiles = removeFileFromArray(files, file2);

    expect(updatedFiles).toEqual([file1, file3]);
    expect(updatedFiles).toHaveLength(2);
    expect(mockRevokeObjectURL).toHaveBeenCalledWith('blob:http://localhost/file2-url');
    expect(mockRevokeObjectURL).toHaveBeenCalledTimes(1);
  });

  it('should return original array if file to remove is not found', () => {
    const file1 = {
      url: 'blob:http://localhost/file1-url',
    };

    const file2 = {
      url: 'blob:http://localhost/file2-url',
    };

    const fileNotInArray = {
      url: 'blob:http://localhost/file3-url',
    };

    const files = [file1, file2];

    const updatedFiles = removeFileFromArray(files, fileNotInArray);

    expect(updatedFiles).toEqual([file1, file2]);
    expect(updatedFiles).toHaveLength(2);
    expect(mockRevokeObjectURL).toHaveBeenCalledWith('blob:http://localhost/file3-url');
    expect(mockRevokeObjectURL).toHaveBeenCalledTimes(1);
  });

  it('should handle empty array', () => {
    const fileToRemove = {
      url: 'blob:http://localhost/file-url',
    };

    const files: { url: string }[] = [];

    const updatedFiles = removeFileFromArray(files, fileToRemove);

    expect(updatedFiles).toEqual([]);
    expect(updatedFiles).toHaveLength(0);
    expect(mockRevokeObjectURL).toHaveBeenCalledWith('blob:http://localhost/file-url');
    expect(mockRevokeObjectURL).toHaveBeenCalledTimes(1);
  });
});
