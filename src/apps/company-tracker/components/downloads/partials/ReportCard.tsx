import React from 'react';
import { Card, CardBody, CardText } from 'reactstrap';
import './ReportCard.scss';
import classNames from 'classnames';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface Props {
  text: string;
  img: string;
  onClick: () => void;
  active?: boolean;
  disabledText?: string;
}

export const ReportCard = (props: Props) => {
  const { text, img, active = false, onClick, disabledText } = props;

  return (
    <SimpleTooltip text={disabledText}>
      <Card
        data-testid={`template-${text}`}
        className={classNames('border-0 rounded shadow-sm', { active: active, 'cursor-pointer': !disabledText })}
        onClick={disabledText ? undefined : onClick}
        disabled={!!disabledText}
      >
        <div className='p-3 rounded-top background-ThemeBgMedium'>
          <img alt={text} src={img} width={145} className='rounded' />
        </div>
        <CardBody className='rounded-bottom'>
          <CardText className='text-center text-uppercase fw-semibold'>{text}</CardText>
        </CardBody>
      </Card>
    </SimpleTooltip>
  );
};
