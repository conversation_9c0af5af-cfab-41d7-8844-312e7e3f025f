@import "src/css/variables";

@mixin color-scheme-highlight {
  background-color: var(--theme-AccentExtralight);
  border-radius: $border-radius;
}

.scheme-container {
  .color-scheme-container {
    .color-scheme-item {
      width: 24px;
      height: 24px;
    }
    &:hover {
      @include color-scheme-highlight();
    }
    &.active {
      @include color-scheme-highlight();
    }
  }
  .disabled {
    color: var(--theme-TextPlaceholder);
    .color-scheme-container {
      &:hover {
        background-color: unset;
        border-radius: unset;
      }
    }
  }
}
