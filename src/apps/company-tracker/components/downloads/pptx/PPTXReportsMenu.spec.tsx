import { render, screen } from '@testing-library/react';
import { PPTXReportsMenu } from './PPTXReportsMenu';
import { PPTXReportScheme, PPTXTemplateScheme, PPTXColorScheme } from '@g17eco/types/download';
import { AI_MESSAGE } from '@constants/ai';
import { useAppSelector } from '@reducers/index';
import { JobStatus } from '@g17eco/types/background-jobs';
import { Mock } from 'vitest';
import { useAIInstructionsForm } from '@features/ai/ai-instructions-form/useAIInstructionsForm';
import userEvent from '@testing-library/user-event';
import { useAppSettings } from '@hooks/app/useAppSettings';

// Mock the Redux selector
vi.mock('@reducers/index', () => ({
  useAppSelector: vi.fn(),
}));

vi.mock('@hooks/app/useAppSettings', () => ({
  useAppSettings: vi.fn(),
}));

vi.mock('@features/ai/ai-instructions-form/useAIInstructionsForm');

const mockOnChangeForm = vi.fn();
const mockSaveToStorage = vi.fn();

describe('PPTXReportsMenu', () => {
  const defaultScheme: PPTXReportScheme = {
    template: PPTXTemplateScheme.Default,
    color: PPTXColorScheme.Ocean,
  };

  const defaultAiConfig = {
    additionalContext: '',
  };

  const defaultProps = {
    generatingReport: undefined,
    generateReport: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useAppSelector as Mock).mockReturnValue(false); // isStaff = false
    (useAIInstructionsForm as Mock).mockReturnValue({
      form: defaultAiConfig,
      onChangeForm: mockOnChangeForm,
      saveToStorage: mockSaveToStorage,
    });
  });

  describe('when useAISlideSuggestions is true', () => {
    beforeEach(() => {
      (useAppSettings as Mock).mockReturnValue({ useAISlideSuggestions: true });
    });

    it('should render cards for all templates', async () => {
      render(<PPTXReportsMenu {...defaultProps} />);

      // display privacy protection message about private metrics
      expect(screen.getByText('AI Report Generation:')).toBeInTheDocument();
      expect(screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).toBeInTheDocument();

      // display the privacy alert
      const alert = screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC).closest('.alert');
      expect(alert).toBeInTheDocument();

      // display the info icon in the privacy alert
      const infoIcon = screen
        .getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)
        .closest('.alert')
        ?.querySelector('i.fal.fa-info-circle');
      expect(infoIcon).toBeInTheDocument();

      // Check that all buttons are present
      expect(screen.getByText('Generate Report')).toBeInTheDocument();
      expect(screen.queryByText('DEBUG')).not.toBeInTheDocument(); // staff only

      // And the privacy message is there
      expect(screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).toBeInTheDocument();

      // check templates visibility
      const minimalistCard = screen.getByTestId('template-Minimalist');
      expect(minimalistCard).not.toHaveAttribute('disabled');

      const aspectCard = screen.getByTestId('template-Hexagons');
      expect(aspectCard).toHaveAttribute('disabled');
      await userEvent.hover(aspectCard);
      const hexagonsTooltip = await screen.findByRole('tooltip');
      expect(hexagonsTooltip).toHaveTextContent('Coming soon');

      const blocksCard = screen.getByTestId('template-Blocks');
      expect(blocksCard).toHaveAttribute('disabled');

      // check colors visibility
      const oceanColor = screen.getByTestId('color-ocean');
      expect(oceanColor).not.toHaveClass('disabled');

      const sunsetColor = screen.getByTestId('color-sunset');
      expect(sunsetColor).toHaveClass('disabled');
      await userEvent.hover(sunsetColor);
      const sunsetTooltip = await screen.findByRole('tooltip');
      expect(sunsetTooltip).toHaveTextContent('Coming soon');

      const valleyColor = screen.getByTestId('color-valley');
      expect(valleyColor).toHaveClass('disabled');
    });
  });

  describe('when useAISlideSuggestions is false', () => {
    beforeEach(() => {
      (useAppSettings as Mock).mockReturnValue({ useAISlideSuggestions: false });
    });

    it('should render cards for all templates', () => {
      render(<PPTXReportsMenu {...defaultProps} />);

      // display privacy protection message for non-staff users
      expect(screen.getByText('AI Report Generation:')).toBeInTheDocument();
      expect(screen.getByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).toBeInTheDocument();

      // render basic report generation button for non-staff users
      expect(screen.getByText('Generate Report')).toBeInTheDocument();
      expect(screen.queryByText('DEBUG')).not.toBeInTheDocument(); // staff only

      // check cards visibility
      expect(screen.getByTestId('template-Minimalist')).not.toHaveAttribute('disabled');
      expect(screen.getByTestId('template-Hexagons')).not.toHaveAttribute('disabled');
      expect(screen.getByTestId('template-Blocks')).not.toHaveAttribute('disabled');

      // check colors visibility
      expect(screen.getByTestId('color-ocean')).not.toHaveClass('disabled');
      expect(screen.getByTestId('color-sunset')).not.toHaveClass('disabled');
      expect(screen.getByTestId('color-valley')).not.toHaveClass('disabled');
    });
  });

  describe('report generation functionality', () => {
    beforeEach(() => {
      (useAppSettings as Mock).mockReturnValue({ useAISlideSuggestions: false });
    });

    it('should call generateReport when Generate Report button is clicked', () => {
      render(<PPTXReportsMenu {...defaultProps} />);

      const generateButton = screen.getByText('Generate Report');
      generateButton.click();

      expect(defaultProps.generateReport).toHaveBeenCalledWith({
        scheme: defaultScheme,
        aiConfig: defaultAiConfig,
      });
    });

    it('should call generateReport when Generate Report when useAISlideSuggestions is true', () => {
      (useAppSettings as Mock).mockReturnValue({ useAISlideSuggestions: true });
      render(<PPTXReportsMenu {...defaultProps} />);

      const generateAIButton = screen.getByText('Generate Report');
      generateAIButton.click();

      expect(defaultProps.generateReport).toHaveBeenCalledWith({
        scheme: defaultScheme,
        aiConfig: defaultAiConfig,
      });
    });

    it('should call generateReport with debug flag when DEBUG button is clicked', () => {
      (useAppSelector as Mock).mockReturnValue(true); // isStaff = true
      (useAppSettings as Mock).mockReturnValue({ useAISlideSuggestions: false });
      render(<PPTXReportsMenu {...defaultProps} />);

      const debugButton = screen.getByText('DEBUG');
      debugButton.click();

      expect(defaultProps.generateReport).toHaveBeenCalledWith({
        scheme: defaultScheme,
        debug: true,
        aiConfig: defaultAiConfig,
      });
    });
  });

  describe('when report is generating', () => {
    const generatingReport = {
      _id: 'test-report-id',
      status: JobStatus.Processing,
      created: new Date().toISOString(),
      user: { _id: 'user-id', firstName: 'Test', surname: 'User' },
      initiativeId: 'initiative-id',
      taskId: 'task-id',
      surveyId: 'survey-id',
      templateScheme: PPTXTemplateScheme.Default,
    };

    beforeEach(() => {
      (useAppSettings as Mock).mockReturnValue({ useAISlideSuggestions: false });
    });

    it('should show work in progress view when report is generating', () => {
      render(<PPTXReportsMenu {...defaultProps} generatingReport={generatingReport} />);

      expect(screen.getByText('AI Enhanced Sustainability Reports')).toBeInTheDocument();
      expect(screen.getByText(/Your report is being generated/)).toBeInTheDocument();
      expect(screen.queryByText('Generate Report')).not.toBeInTheDocument();
      expect(screen.queryByText(AI_MESSAGE.EXCLUDED_PRIVATE_METRIC)).not.toBeInTheDocument();
    });
  });

  describe('AI Instructions', () => {
    it('should open popover on click and show form', async () => {
      const user = userEvent.setup();
      render(<PPTXReportsMenu {...defaultProps} />);

      const aiButton = screen.getByText('AI Instructions');
      await user.click(aiButton);

      expect(screen.getByLabelText('AI Instructions')).toBeInTheDocument();
      expect(screen.getByLabelText('AI Instructions')).toHaveValue('');
    });

    it('should call onChangeForm when user types in textarea', async () => {
      const user = userEvent.setup();
      render(<PPTXReportsMenu {...defaultProps} />);

      const aiButton = screen.getByText('AI Instructions');
      await user.click(aiButton);

      const textarea = screen.getByLabelText('AI Instructions');
      await user.type(textarea, 'a');

      expect(mockOnChangeForm).toHaveBeenCalledWith({ additionalContext: 'a' });
    });
  });
});
