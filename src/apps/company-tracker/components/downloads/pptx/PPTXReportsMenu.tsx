import { DashboardRow, DashboardSection } from '@g17eco/molecules/dashboard';
import {
  PPTXColorScheme,
  PPTXColorSchemeMap,
  PPTXReportScheme,
  PPTXTemplateScheme,
  PPTXTemplateSchemeLabelMap,
} from '@g17eco/types/download';
import { useRef, useState } from 'react';
import { Button, PopoverBody, UncontrolledPopover } from 'reactstrap';
import { WorkInProgress } from '@g17eco/molecules/work-in-progress';
import { ReportCard } from '../partials/ReportCard';
import classnames from 'classnames';
import { capitaliseFirstLetter } from '@utils/index';
import { generateArrayOfNumbers } from '@utils/number';
import { PPTXReportItem } from '@api/pptx-reports';
import { JobStatus } from '@g17eco/types/background-jobs';
import { SURVEY } from '@constants/terminology';
import { isStaff } from '@selectors/user';
import { useAppSelector } from '@reducers/index';
import { FeatureStability } from '@g17eco/molecules/feature-stability';
import { BasicAlert } from '@g17eco/molecules/alert';
import { AI_MESSAGE } from '@constants/ai';
import { AIInstructionsForm } from '@features/ai/ai-instructions-form/AIInstructionsForm';
import { useAIInstructionsForm } from '@features/ai/ai-instructions-form/useAIInstructionsForm';
import { AIConfig } from '@g17eco/types/ai';
import { useAppSettings } from '@hooks/app/useAppSettings';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface Props {
  generatingReport: PPTXReportItem | undefined;
  generateReport: (props: {
    scheme: PPTXReportScheme;
    debug?: boolean;
    aiConfig: AIConfig;
  }) => void;
  handleRetryReport?: (reportId: string) => void;
}

const baseImageUrl = 'https://wwg-cdn.s3.eu-west-2.amazonaws.com/pptx-report/preview';

const PPTX_SCHEMES = [
  {
    label: PPTXTemplateSchemeLabelMap[PPTXTemplateScheme.Default],
    value: PPTXTemplateScheme.Default,
    img: `${baseImageUrl}/Minimalist-Ocean-2.jpg`,
  },
  {
    label: PPTXTemplateSchemeLabelMap[PPTXTemplateScheme.Aspect],
    value: PPTXTemplateScheme.Aspect,
    img: `${baseImageUrl}/Hex-Ocean-2.jpg`,
  },
  {
    label: PPTXTemplateSchemeLabelMap[PPTXTemplateScheme.Blocks],
    value: PPTXTemplateScheme.Blocks,
    img: `${baseImageUrl}/Blocks-Ocean-2.jpg`,
  },
];

const getSchemePreviewImages = ({ template, color }: PPTXReportScheme): string[] => {
  const capitalizedColor = capitaliseFirstLetter(color);
  const imageOrders = generateArrayOfNumbers(1, 4);
  switch (template) {
    case PPTXTemplateScheme.Aspect:
      return imageOrders.map((order) => `${baseImageUrl}/Hex-${capitalizedColor}-${order}.jpg`);
    case PPTXTemplateScheme.Blocks:
      return imageOrders.map((order) => `${baseImageUrl}/Blocks-${capitalizedColor}-${order}.jpg`);
    case PPTXTemplateScheme.Default:
    default:
      return imageOrders.map((order) => `${baseImageUrl}/Minimalist-${capitalizedColor}-${order}.jpg`);
  }
};

const title = 'AI Enhanced Sustainability Reports';

const ColorScheme = ({ colors, isActive }: { colors: string[]; isActive: boolean }) => (
  <div className={classnames('d-flex gap-2 p-2 color-scheme-container', { active: isActive })}>
    {colors.map((color, idx) => (
      <div
        key={idx}
        className={classnames('color-scheme-item', {
          'border border-ThemeBorderDefault': idx === colors.length - 1,
        })}
        style={{
          backgroundColor: color,
        }}
      />
    ))}
  </div>
);

export const PPTXReportsMenu = (props: Props) => {
  const { generatingReport, generateReport, handleRetryReport } = props;
  const isStaffUser = useAppSelector(isStaff);
  const { useAISlideSuggestions } = useAppSettings();
  const [scheme, setScheme] = useState<PPTXReportScheme>({
    template: PPTXTemplateScheme.Default,
    color: PPTXColorScheme.Ocean,
  });
  const { form, onChangeForm, saveToStorage } = useAIInstructionsForm({
    storageKey: 'ai-instructions-pptx',
  });

  const aiInstructionsBtnRef = useRef<HTMLButtonElement>(null);

  const isAllowedRetry = generatingReport && generatingReport.status === JobStatus.Error;

  const jobMessage = (
    <div className='text-center text-ThemeTextMedium'>
      {handleRetryReport && isAllowedRetry ? (
        <>
          <span>An error occurred while generating your report.</span>
          <br />
          <span>Please use the button below to try regenerating it.</span>
          <br />
          <span>You will be notified by email when the report is ready.</span>
        </>
      ) : (
        <>
          <span>
            Your report is being generated and will appear in the ‘Latest reports’ section below in a couple of minutes.
          </span>
          <br /> You can leave this page and return later. <br />
          <span>We will send you a notification and an email when the report is ready.</span>
        </>
      )}
      <br />
      {handleRetryReport ? (
        <Button
          className='mt-2'
          disabled={!isAllowedRetry}
          onClick={() => handleRetryReport(generatingReport?._id ?? '')}
        >
          <i className='fas fa-arrow-rotate-right mr-2' />
          {`Regenerate ${SURVEY.SINGULAR}`}
        </Button>
      ) : null}
    </div>
  );

  const shouldDisableTemplate = (value: PPTXTemplateScheme) => {
  return useAISlideSuggestions && [PPTXTemplateScheme.Aspect, PPTXTemplateScheme.Blocks].includes(value);
};

const shouldDisableColor = (value: PPTXColorScheme) => {
  return useAISlideSuggestions && [PPTXColorScheme.Sunset, PPTXColorScheme.Valley].includes(value);
};

  const handleUpdateScheme = (key: keyof PPTXReportScheme, value: PPTXTemplateScheme | PPTXColorScheme) => {
    if (shouldDisableTemplate(value as PPTXTemplateScheme) || shouldDisableColor(value as PPTXColorScheme)) {
      return;
    }
    setScheme((prev) => ({ ...prev, [key]: value }));
  };

  if (generatingReport) {
    return (
      <DashboardRow title={title}>
        <WorkInProgress title={jobMessage} />
      </DashboardRow>
    );
  }

  const images = getSchemePreviewImages(scheme);

  return (
    <>
      <h3 className='text-ThemeHeadingDark px-3 mb-3'>{title}</h3>
      <div className='d-flex align-items-center justify-content-between gap-2 mb-3 px-3'>
        <div className='me-auto'>
          <Button size='sm' color='secondary' innerRef={aiInstructionsBtnRef}>
            <i className='fa-light fa-input-text mr-2' /> AI Instructions
          </Button>
          <UncontrolledPopover
            placement='bottom'
            container='body'
            hideArrow
            trigger='legacy' // trigger='legacy' is required for onBlur to work
            target={aiInstructionsBtnRef}
            onBlur={saveToStorage}
            className='ai-instructions-popover'
          >
            <PopoverBody>
              <AIInstructionsForm form={form} onChangeForm={onChangeForm} />
            </PopoverBody>
          </UncontrolledPopover>
        </div>
        <Button
          className='px-4 py-2 text-lg fw-semibold'
          color='primary'
          onClick={() => generateReport({ scheme, aiConfig: form })}
        >
          <i className='fal fa-file mr-1 text-xl' /> Generate Report
        </Button>
        {isStaffUser && (
          <Button
            className='px-4 py-2 text-lg'
            color='primary'
            outline
            onClick={() => generateReport({ scheme, debug: true, aiConfig: form })}
          >
            <i className='fal fa-file-powerpoint mr-1 text-xl' /> DEBUG
            <FeatureStability stability='internal' />
          </Button>
        )}
      </div>
      <BasicAlert type='info' className='mx-3'>
        <div className='d-flex align-items-center'>
          <i className='fal fa-info-circle mr-2' />
          <span>
            <strong>AI Report Generation:</strong> {AI_MESSAGE.EXCLUDED_PRIVATE_METRIC}
          </span>
        </div>
      </BasicAlert>
      <DashboardSection title='Selected PowerPoint report theme' padding={0}>
        <div className='d-flex align-items-center justify-content-between gap-5 scheme-container'>
          <div className='d-flex justify-content-center gap-4 mb-3'>
            {PPTX_SCHEMES.map(({ label, value, img }, idx) => (
              <ReportCard
                key={idx}
                text={label}
                img={img}
                onClick={() => handleUpdateScheme('template', value)}
                active={value === scheme.template}
                disabledText={shouldDisableTemplate(value) ? 'Coming soon' : undefined}
              />
            ))}
          </div>
          <div>
            {Object.entries(PPTXColorSchemeMap).map(([color, colors]) => {
              const colorScheme = color as PPTXColorScheme;
              const disabled = shouldDisableColor(colorScheme);
              return (
                <div
                  key={colorScheme}
                  data-testid={`color-${colorScheme}`}
                  className={classnames('d-flex align-items-center gap-3', {
                    'cursor-pointer': !disabled,
                    disabled: disabled,
                  })}
                  onClick={() => handleUpdateScheme('color', colorScheme)}
                >
                  <SimpleTooltip text={disabled ? 'Coming soon' : undefined}>
                    <ColorScheme colors={colors} isActive={colorScheme === scheme.color} />
                  </SimpleTooltip>
                  <span className='fw-semibold'>{colorScheme.toUpperCase()}</span>
                </div>
              );
            })}
          </div>
        </div>
      </DashboardSection>
      <DashboardRow className='py-2'>
        <div className='d-flex justify-content-center w-100 gap-2'>
          {images.map((url) => (
            <img key={url} src={url} width={210} />
          ))}
        </div>
      </DashboardRow>
    </>
  );
};
