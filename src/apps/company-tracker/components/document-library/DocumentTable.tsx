import NoDocuments from '@g17eco/images/no-documents-in-library.svg';
import { BasicAlert } from '@g17eco/molecules/alert';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { useDeleteDocumentMutation, useGetDocumentsQuery, useUpdateDocumentMutation } from '@api/documents';
import { useMemo, useState } from 'react';
import { DocumentItem } from '@g17eco/types/document';
import { DocumentFilters } from './DocumentFilters';
import { generateDocumentsFlexSearchMap } from '@features/admin-dashboard/utils';
import { Checkbox, CheckboxState } from '@g17eco/atoms/checkbox';
import { DATE, formatDateUTC } from '@utils/date';
import { Button, DropdownItem, DropdownMenu, DropdownToggle, UncontrolledDropdown } from 'reactstrap';
import { DocumentToolbar } from './DocumentToolbar';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { documentOptions } from '@features/document-library/utils';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { DocumentModal, DocumentViewMode } from '@features/document-library/DocumentModal';

export type FilterParams = {
  searchText: string;
  type: string;
};

export const DocumentTable = ({ initiativeId }: { initiativeId: string }) => {
  const [filters, setFilters] = useState<FilterParams>({ searchText: '', type: '' });
  const [selectedDocument, setSelectedDocument] = useState<{ document: DocumentItem; mode: DocumentViewMode }>();
  const [selectedDocuments, setSelectedDocuments] = useState<DocumentItem[]>([]);
  const isDocumentSelected = (docId: string) => selectedDocuments.some((doc) => doc._id === docId);
  const { addSiteAlert, addSiteError } = useSiteAlert();
  const { data, isFetching, error } = useGetDocumentsQuery({ initiativeId });
  const { documents: listDocuments = [] } = data ?? {};
  const [deleteDocument] = useDeleteDocumentMutation();
  const [updateDocument, { isLoading: isUpdating }] = useUpdateDocumentMutation();

  const columns: ColumnDef<DocumentItem>[] = [
    {
      id: 'multi-select-action',
      meta: {
        cellProps: { style: { width: 20 }, className: 'text-center' },
      },
      header: '',
      cell: (c) => {
        const document = c.row.original;
        const status = isDocumentSelected(document._id) ? CheckboxState.Checked : CheckboxState.Unchecked;
        return (
          <Checkbox
            status={status}
            onChange={() => {
              const newSelectedDocuments =
                status === CheckboxState.Checked
                  ? selectedDocuments.filter((q) => q._id !== document._id)
                  : [...selectedDocuments, document];

              return setSelectedDocuments(newSelectedDocuments);
            }}
          />
        );
      },
    },
    {
      accessorKey: 'ownerSubType',
      header: 'Type',
      meta: {
        headerProps: {
          style: {
            width: 230,
          },
        },
      },
      cell: ({ row }) => {
        return (
          <div data-testid='document-type-dropdown'>
            <SelectFactory
              className='w-100 border-none'
              selectType={SelectTypes.SingleSelect}
              placeholder='Select a type'
              options={documentOptions}
              value={documentOptions.find(({ value }) => value === row.original.ownerSubType)}
              isDisabled={isUpdating}
              onChange={(op) => {
                handleUpdateDocument({ ...row.original, ownerSubType: op?.value });
              }}
              isSearchable={false}
              menuPlacement='bottom'
              isMenuPortalTargetBody
              isTransparent={true}
            />
          </div>
        );
      },
    },
    {
      header: 'File name',
      cell: ({ row }) => {
        const limit = 40;
        return (
          <SimpleTooltip text={row.original.description}>
            <Button
              color='link-secondary'
              onClick={() => setSelectedDocument({ document: row.original, mode: DocumentViewMode.View })}
            >
              <span className='text-ThemeTextMedium'>{row.original.title}</span>
            </Button>
          </SimpleTooltip>
        );
      },
    },
    { accessorKey: 'metadata.extension', header: 'Format' },
    {
      accessorKey: 'created',
      header: 'Upload date',
      cell: ({ row }) => formatDateUTC(row.original.created, DATE.YEAR_MONTH_DATE),
    },
    {
      id: 'action-buttons',
      header: '',
      meta: {
        headerProps: {
          style: {
            width: 50,
          },
        },
      },
      cell: ({ row }) => (
        <UncontrolledDropdown>
          <DropdownToggle color='transparent' outline className='px-2'>
            <i className='fas fa-bars' />
          </DropdownToggle>
          <DropdownMenu>
            <DropdownItem
              size='sm'
              onClick={() => setSelectedDocument({ document: row.original, mode: DocumentViewMode.Edit })}
            >
              <i className='fal fa-pencil mr-2' />
              Edit
            </DropdownItem>
            <DropdownItem
              size='sm'
              onClick={() => setSelectedDocument({ document: row.original, mode: DocumentViewMode.Delete })}
            >
              <i className='fal fa-trash mr-2' />
              Delete
            </DropdownItem>
          </DropdownMenu>
        </UncontrolledDropdown>
      ),
    },
  ];

  const filteredRows: DocumentItem[] = useMemo(() => {
    const { searchText, type } = filters;
    let rows = listDocuments ?? [];
    if (searchText) {
      const searchIndex = generateDocumentsFlexSearchMap(listDocuments);
      const result = searchIndex.search(searchText);
      const matchedIds = new Set(result.map((item) => item.result).flat());
      rows = rows.filter((doc) => matchedIds.has(doc._id));
    }
    if (type) {
      rows = rows.filter((doc) => doc.ownerSubType === type);
    }
    return rows;
  }, [filters, listDocuments]);

  const handleChangeFilters = (key: keyof FilterParams, value?: string) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleUpdateDocument = (data: DocumentItem) => {
    updateDocument({ initiativeId, documentId: data._id, data })
      .unwrap()
      .then(() => {
        addSiteAlert({
          content: (
            <div className='text-center'>
              <i className='fal fa-check-circle mr-2' />
              Document has been updated
            </div>
          ),
        });
      })
      .catch((error) => {
        addSiteError(error);
      });
  };

  const handleDeleteDocument = (deletingDocumentId?: string) => {
    if (!deletingDocumentId) {
      return;
    }
    deleteDocument({ initiativeId, documentId: deletingDocumentId })
      .unwrap()
      .then(() => {
        addSiteAlert({
          content: (
            <div className='text-center'>
              <i className='fal fa-check-circle mr-2' />
              Document deleted
            </div>
          ),
        });
      })
      .catch((error) => {
        addSiteError(error);
      })
      .finally(() => {
        setSelectedDocument(undefined);
        setSelectedDocuments([]);
      });
  };

  return (
    <>
      {isFetching ? <BlockingLoader /> : null}
      {error ? <BasicAlert type='danger'>{error.message}</BasicAlert> : null}
      {listDocuments.length === 0 && !isFetching ? (
        <div className='text-center'>
          <h1 className='text-ThemeTextPlaceholder mb-5'>No documents uploaded yet!</h1>
          <img src={NoDocuments} alt='no documents in library' />
        </div>
      ) : (
        <>
          <DocumentFilters filters={filters} onChangeFilters={handleChangeFilters} />
          <div data-testid='manage-documents-table'>
            <Table
              pageSize={10}
              columns={columns}
              data={filteredRows}
              className='mt-3'
              responsive
              noData={<BasicAlert type='secondary'>No documents found</BasicAlert>}
            />
          </div>
        </>
      )}
      <DocumentToolbar
        initiativeId={initiativeId}
        selectedDocuments={selectedDocuments}
        setSelectedDocuments={setSelectedDocuments}
      />
      {selectedDocument ? (
        <DocumentModal
          isOpen={true}
          mode={selectedDocument.mode}
          document={selectedDocument.document}
          toggle={() => setSelectedDocument(undefined)}
          handleUpdate={handleUpdateDocument}
          handleDelete={handleDeleteDocument}
        />
      ) : null}
    </>
  );
};
