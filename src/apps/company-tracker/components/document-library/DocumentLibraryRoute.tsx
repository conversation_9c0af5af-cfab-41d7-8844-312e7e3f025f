import Dashboard, { DashboardRow, DashboardSection, DashboardSectionTitle } from '@g17eco/molecules/dashboard';
import { Route, Switch, useParams, useRouteMatch } from 'react-router-dom';
import { CTAdminBreadcrumbs } from '../breadcrumbs/CTAdminBreadcrumbs';
import { Button } from 'reactstrap';
import { useToggle } from '@hooks/useToggle';
import { DocumentTable } from './DocumentTable';
import { DocumentUpload } from './DocumentUpload';
import { useUploadDocumentsMutation } from '@api/documents';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { SiteAlertColors } from '@g17eco/slices/siteAlertsSlice';
import { useState } from 'react';
import { checkHasReachedMaxSize, MAX_SIZE_MB } from '@utils/files';
import { BasicAlert } from '@g17eco/molecules/alert';

const renderUploadButton = ({ disabled, toggleUpload }: { disabled?: boolean; toggleUpload: () => void }) => {
  return [
    <Button key='upload-btn' disabled={disabled} onClick={() => toggleUpload()} color='secondary'>
      <i className='fal fa-files-medical mr-2' />
      Upload documents
    </Button>,
  ];
};

export const DocumentLibraryRoute = () => {
  const [openUpload, toggleUpload] = useToggle(false);
  const { initiativeId } = useParams<{ initiativeId: string }>();
  const { path } = useRouteMatch();
  const [uploadDocument, { isLoading: isUploading }] = useUploadDocumentsMutation();
  const { addSiteAlert, addSiteError } = useSiteAlert();
  const [errorMessage, setErrorMessage] = useState<string>();

  const handleFilesAdded = (files: File[]) => {
    if (files.length === 0) {
      return;
    }

    if (checkHasReachedMaxSize(files)) {
      setErrorMessage(`Total size of files is larger than ${MAX_SIZE_MB}MB`);
      return;
    }

    setErrorMessage(undefined);

    uploadDocument({ initiativeId, data: { files } })
      .unwrap()
      .then(({ fulfilled, rejected }) => {
        const isAllFulfilled = fulfilled.length === files.length;
        const isAllRejected = rejected.length === files.length;

        addSiteAlert({
          content: (
            <div className='text-center'>
              <i className='fal fa-check-circle mr-2' />
              {fulfilled.length} document(s) uploaded successfully. {rejected.length} document(s) failed to upload.
            </div>
          ),
          color: isAllFulfilled
            ? SiteAlertColors.Success
            : isAllRejected
              ? SiteAlertColors.Danger
              : SiteAlertColors.Warning,
        });
      })
      .catch((error) => {
        addSiteError(error);
      })
      .finally(() => {
        toggleUpload();
      });
  };

  return (
    <Switch>
      <Route path={path}>
        <Dashboard>
          <DashboardRow>
            <CTAdminBreadcrumbs breadcrumbs={[{ label: 'Evidence library' }]} initiativeId={initiativeId} />
          </DashboardRow>
          <DashboardSectionTitle
            title='Document Library'
            buttons={renderUploadButton({ disabled: isUploading, toggleUpload })}
          />
          <DashboardRow isVisible={openUpload} classes={{ children: 'flex-lg-column' }}>
            <BasicAlert type={'danger'} hide={!errorMessage}>{errorMessage}</BasicAlert>
            <DocumentUpload isUploading={isUploading} handleFilesAdded={handleFilesAdded} />
          </DashboardRow>
          <DashboardSection>
            <DocumentTable initiativeId={initiativeId} />
          </DashboardSection>
        </Dashboard>
      </Route>
    </Switch>
  );
};
