import { DocumentItem, DocumentSubType, DocumentMediaType, InitiativeDocument } from '@g17eco/types/document';
import { g17ecoApi, transformResponse } from './g17ecoApi';
import { formDataHeaders } from '@services/G17Client';

enum Tag {
  Documents = 'documents',
  Document = 'document',
  DisplayDocuments = 'display-documents',
}

export interface QueryStats {
  initiativeId: string;
  startDate?: Date;
  endDate?: Date;
  cursor?: string;
  limit?: number;
  mediaTypes?: DocumentMediaType[];
  ownerSubType?: DocumentSubType;
  searchText?: string;
}

export type DocumentsByType = {
  [DocumentMediaType.Image]: InitiativeDocument[];
  [DocumentMediaType.Video]: InitiativeDocument[];
  [DocumentMediaType.File]: InitiativeDocument[];
  [DocumentMediaType.Assurance]: InitiativeDocument[];
};

export type DocumentChanges = { addedIds: string[]; removedIds: string[] };

const getUrl = (initiativeId: string) => `/initiatives/${initiativeId}/documents`;

export const DocumentsApi = g17ecoApi
  .enhanceEndpoints({
    addTagTypes: Object.values(Tag),
  })
  .injectEndpoints({
    endpoints: (build) => ({
      uploadDocuments: build.mutation<
        { fulfilled: DocumentItem[]; rejected: any[] },
        { initiativeId: string; data: { files: File[] } }
      >({
        invalidatesTags: (_result, _error, { initiativeId }) => [{ type: Tag.Documents, id: initiativeId }],
        transformResponse,
        query: ({ initiativeId, data }) => ({
          url: getUrl(initiativeId),
          headers: formDataHeaders,
          method: 'post',
          data: data,
        }),
      }),
      getDocuments: build.query<{ documents: DocumentItem[]; nextCursor?: string; hasNextPage: boolean }, QueryStats>({
        transformResponse,
        query: ({ initiativeId, ...params }) => ({
          url: getUrl(initiativeId),
          method: 'get',
          params,
        }),
        providesTags: (_result, _error, { initiativeId }) => {
          return [{ type: Tag.Documents, id: initiativeId }];
        },
      }),
      bulkUpdateDocument: build.mutation<
        {
          acknowledged: Boolean;
          matchedCount: number;
          modifiedCount: number;
          upsertedCount: number;
          upsertedIds: string[];
        },
        { initiativeId: string; data: { documentIds: string[]; title?: string; ownerSubType?: DocumentSubType } }
      >({
        invalidatesTags: (_result, _error, { initiativeId }) => [{ type: Tag.Documents, id: initiativeId }],
        transformResponse,
        query: ({ initiativeId, data }) => ({
          url: getUrl(initiativeId),
          method: 'patch',
          data,
        }),
      }),
      updateDocument: build.mutation<
        DocumentItem,
        { initiativeId: string; documentId: string; data: { title?: string; ownerSubType?: DocumentSubType } }
      >({
        invalidatesTags: (_result, _error, { initiativeId, documentId }) => [
          { type: Tag.Documents, id: initiativeId },
          { type: Tag.Document, id: documentId },
        ],
        transformResponse,
        query: ({ initiativeId, documentId, data }) => ({
          url: `${getUrl(initiativeId)}/${documentId}`,
          method: 'patch',
          data,
        }),
      }),
      deleteDocument: build.mutation<DocumentItem, { initiativeId: string; documentId: string }>({
        invalidatesTags: (_result, _error, { initiativeId }) => [
          { type: Tag.Documents, id: initiativeId },
          { type: Tag.DisplayDocuments, id: initiativeId },
        ],
        transformResponse,
        query: ({ initiativeId, documentId }) => ({
          url: `${getUrl(initiativeId)}/${documentId}`,
          method: 'delete',
        }),
      }),
      getDisplayDocumentsByType: build.query<DocumentsByType, string>({
        transformResponse,
        query: (initiativeId) => ({
          url: `${getUrl(initiativeId)}/insight-display`,
          method: 'get',
        }),
        providesTags: (_result, _error, initiativeId) => [{ type: Tag.DisplayDocuments, id: initiativeId }],
      }),
      updateDisplayDocuments: build.mutation<
        string[],
        { initiativeId: string; data: { documentChanges: DocumentChanges } }
      >({
        invalidatesTags: (_result, _error, { initiativeId }) => [{ type: Tag.DisplayDocuments, id: initiativeId }],
        transformResponse,
        query: ({ initiativeId, data: { documentChanges } }) => ({
          url: `${getUrl(initiativeId)}/insight-display`,
          method: 'patch',
          data: documentChanges,
        }),
      }),
    }),
  });

export const {
  useGetDocumentsQuery,
  useLazyGetDocumentsQuery,
  useBulkUpdateDocumentMutation,
  useUpdateDocumentMutation,
  useDeleteDocumentMutation,
  useUploadDocumentsMutation,
  useGetDisplayDocumentsByTypeQuery,
  useUpdateDisplayDocumentsMutation,
} = DocumentsApi;
