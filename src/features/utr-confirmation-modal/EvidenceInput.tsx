/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { FileDropZone } from '@features/files/FileDropZone'
import {
  InputGroup,
  InputGroupText,
  Input,
  Button
} from 'reactstrap'
import { FileListContainer, FileListContainerProps } from '@features/files/FileListContainer'
import React from 'react'
import { SupportingEvidenceTooltip } from './SupportingEvidenceTooltip';
import { CollapseButton, CollapseContent, CollapsePanel } from '@g17eco/molecules/collapse-panel';
import { EvidenceLink } from '../../types/universalTracker';
import { AddToLibraryFn, HandleFileDescriptionFn } from '../../types/file';
import { QUESTION } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';
import { useToggle } from '@hooks/useToggle';
import { DocumentsModal } from '@features/document-library/documents-modal/DocumentsModal';
import { DocumentItem, DocumentModalAction } from '@g17eco/types/document';
import { EvidenceFile, ExistingEvidenceFile } from '@g17eco/types/questionInterfaces';
import { mapDocumentsToEvidenceFiles } from '@utils/evidence';

const isExistingEvidence = (evidence: EvidenceFile): evidence is ExistingEvidenceFile => '_id' in evidence;

const getMergedDocuments = (
  existingFiles: EvidenceFile[],
  documentsWithAction: { item: DocumentItem, action: DocumentModalAction }[],
) => {
  const addedDocuments = mapDocumentsToEvidenceFiles(
    documentsWithAction.filter((doc) => doc.action === DocumentModalAction.Add).map((doc) => doc.item),
  );

  const removedIds = new Set(
    documentsWithAction.filter((doc) => doc.action === DocumentModalAction.Remove).map((doc) => doc.item._id),
  );

  const allDocumentsMap = new Map<string, ExistingEvidenceFile>();

  for (const doc of existingFiles.filter(isExistingEvidence)) {
    allDocumentsMap.set(doc._id, doc);
  }

  for (const doc of addedDocuments) {
    allDocumentsMap.set(doc._id, doc);
  }

  return Array.from(allDocumentsMap.values()).filter((doc) => !removedIds.has(doc._id));
};

interface EvidenceProps extends Omit<FileListContainerProps, 'metadata' | 'existingFiles'> {
  disabled: boolean;
  handleEvidenceLinkAdd: (link: any, isPublic: boolean) => void;
  handleFileDescriptionAdd: HandleFileDescriptionFn;
  handleAddToLibrary: AddToLibraryFn;
  handleFilesAdded: (addFiles: File[]) => void;
  isEvidenceRequired?: boolean;
  evidenceInstructions: string;
  isContributor: boolean;
  isVerifier: boolean;
  initiativeId: string;
  setFiles: (addFiles: EvidenceFile[]) => void;
  existingFiles: ExistingEvidenceFile[];
}

const defaultEvidenceLink: EvidenceLink = { url: '', description: '' };

export const EvidenceInput = (props: EvidenceProps) => {
  const [openDocumentsModal, toggleDocumentsModal] = useToggle(false);
  const [publicEvidenceLink, setPublicLink] = React.useState<EvidenceLink>(defaultEvidenceLink);
  const [privateEvidenceLink, setPrivateLink] = React.useState<EvidenceLink>(defaultEvidenceLink);

  const {
    disabled,
    handleEvidenceLinkAdd,
    handleFilesAdded,
    isEvidenceRequired,
    evidenceInstructions,
    isContributor,
    isVerifier,
    existingFiles,
    files,
    initiativeId,
    setFiles,
    handleAddToLibrary,
  } = props;

  const hasAction = isContributor || isVerifier;
  const hasExistingFiles = existingFiles.length > 0;

  const updateLink = (value: EvidenceLink, isPublic: boolean) => {
    handleEvidenceLinkAdd(value, isPublic);
    if (isPublic) {
      setPublicLink(defaultEvidenceLink);
    } else {
      setPrivateLink(defaultEvidenceLink);
    }
  }

  const handleSubmitDocuments = (documentsWithAction: { item: DocumentItem, action: DocumentModalAction }[]) => {
    setFiles(getMergedDocuments(files, documentsWithAction));
    toggleDocumentsModal();
  }

  const hasData = files.length > 0 || hasExistingFiles;
  const documentIds = files.filter(isExistingEvidence).map((file) => file._id);
  const disabledDocumentIds = existingFiles.map((file) => file._id);

  return (
    <CollapsePanel className='py-3 border-top border-bottom' collapsed={!isEvidenceRequired && !hasExistingFiles}>
      <CollapseButton classes={{ content: 'd-block' }}>
        <h5 className='d-flex align-items-center gap-3 text-ThemeAccentExtradark'>
          Supporting Evidence {isEvidenceRequired ? '*' : ''}
          <div className='evidence-tooltip-icon'>
            <SupportingEvidenceTooltip tooltip={evidenceInstructions} />
          </div>
        </h5>
      </CollapseButton>
      <CollapseContent>
        <div className='form-confirmation-input pt-3'>
          {hasAction && !disabled ? (
            <div>
              <div className='d-flex mb-3 gap-1 justify-content-center'>
                <Button color='primary' outline size='lg' onClick={toggleDocumentsModal}>
                  <i className='fal fa-folders mr-2' />
                  Search Document Library
                </Button>
                {openDocumentsModal ? (
                  <DocumentsModal
                    modalTitle={'Add evidence'}
                    key={documentIds.length}
                    handleSubmitDocuments={handleSubmitDocuments}
                    documentIds={documentIds}
                    disabledDocumentIds={disabledDocumentIds}
                    initiativeId={initiativeId}
                    isOpen={openDocumentsModal}
                    toggle={toggleDocumentsModal}
                  />
                ) : null}
              </div>
              <FileDropZone disabled={disabled} multiple={true} onDrop={handleFilesAdded} />

              <div className='d-flex mt-3 gap-1 align-items-center'>
                <InputGroup>
                  <InputGroupText>
                    <i className='fa fa-link' />
                  </InputGroupText>
                  <Input
                    key='input-evidence-link'
                    type='text'
                    disabled={disabled}
                    placeholder='Add a link to EXTERNAL files'
                    value={publicEvidenceLink.url}
                    onChange={(e) => setPublicLink({ ...publicEvidenceLink, url: e.target.value })}
                  />
                </InputGroup>
                <InputGroup style={{ width: '35%' }}>
                  <Input
                    key='evidence-link-description'
                    type='text'
                    disabled={disabled}
                    placeholder='Page reference if applicable'
                    value={publicEvidenceLink.description}
                    onChange={(e) => setPublicLink({ ...publicEvidenceLink, description: e.target.value })}
                  />
                </InputGroup>
                <Button
                  color='primary'
                  disabled={!publicEvidenceLink.url}
                  onClick={() => updateLink(publicEvidenceLink, true)}
                >
                  Add
                </Button>
              </div>

              <div className='d-flex mt-3 gap-1 align-items-center'>
                <InputGroup>
                  <InputGroupText>
                    <i className='fa fa-lock' />
                  </InputGroupText>
                  <Input
                    key='input-evidence-link'
                    type='text'
                    disabled={disabled}
                    placeholder='Add a reference or link to INTERNAL files'
                    value={privateEvidenceLink.url}
                    onChange={(e) => setPrivateLink({ ...privateEvidenceLink, url: e.target.value })}
                  />
                </InputGroup>
                <InputGroup style={{ width: '35%' }}>
                  <Input
                    key='evidence-link-description'
                    type='text'
                    disabled={disabled}
                    placeholder='Page reference if applicable'
                    value={privateEvidenceLink.description}
                    onChange={(e) => setPrivateLink({ ...privateEvidenceLink, description: e.target.value })}
                  />
                </InputGroup>
                <Button
                  color='primary'
                  disabled={!privateEvidenceLink.url}
                  onClick={() => updateLink(privateEvidenceLink, false)}
                >
                  Add
                </Button>
              </div>
            </div>
          ) : null}

          {!hasAction && !hasData ? (
            <BasicAlert type='info'>There are no evidence files for this {QUESTION.SINGULAR}</BasicAlert>
          ) : (
            <FileListContainer
              title={'Evidence to be added'}
              {...props}
              handleAddToLibrary={handleAddToLibrary}
              allowToggle={props.isContributor}
              metadata={{ initiativeId }}
            />
          )}
        </div>
      </CollapseContent>
    </CollapsePanel>
  );
}
