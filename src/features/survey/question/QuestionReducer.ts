/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React, { useCallback } from 'react'
import {
  toggleAddFileToLibrary,
  handleEvidenceLinkAdd,
  handleFileDescriptionAdd,
  removeFiles,
  resolveFiles
} from '../../../utils/files'
import { getTableConfiguration, isSingleRowTableType } from '../../../utils/universalTracker';
import {
  getInputValue,
  getNumberScale,
  getUnitCode,
} from '../../../utils/universalTrackerValue';
import {
  AssuranceState,
  ChangeQuestionProps,
  EvidenceFile,
  ExistingEvidenceFile,
  InitialResettableQuestionState,
  QuestionReducerState,
  TableDataInfo,
  ErrorMessageType,
} from '@g17eco/types/questionInterfaces';
import { tableDataToView } from '../../../utils/valueDataTable';
import { UtrvHistory } from '../../../types/universalTrackerValue';
import { UtrvPermissions } from '../../../services/permissions/UtrvPermissions';
import { EditorState } from 'lexical';
import { getDisplayCheckboxAndOriginalValueData } from './questionUtil';
import { generateDecimalErrorMessage } from '@utils/survey/input';
import { EvidenceLink, ValueTable } from '../../../types/universalTracker';
import { NewEvidenceFile, UpdateDescriptionParams } from '../../../types/file';


const assuranceState: AssuranceState = {
  assurancePortfolio: undefined,
  assurancePortfolioLoaded: false,
  hasPortfolio: false
};

// All of this state will be reset on question change
export const getInitialState = (): InitialResettableQuestionState => ({
  table: {
    editRowId: -1,
    rows: []
  },
  loaded: false,
  displayCheckbox: {},
  displayConfirmation: false,
  action: '',
  saving: false,
  errored: false,
  message: undefined,
  inputMessage: undefined,
  comments: '', // Must be string to initialize textarea as controlled comp
  editorState: undefined,
  files: [],
  existingFiles: [],
  details: {
    loaded: false,
    data: {},
  },
  canContribute: false,
  canVerify: false,
  isNA: false,
  value: undefined,
  valueData: {
    data: undefined
  },
  numberScale: undefined,
  unit: undefined,
  alternativeCode: '',
  alternativeCodeOverride: '',
});

interface ReducerAction {
  type: string;
  data?: Partial<QuestionReducerState> | { data?: UtrvHistory };
  comments?: string; // old plain text note
  editorState?: EditorState; // rich text note
  table?: Partial<TableDataInfo>;
  questionChange?: ChangeQuestionProps;
  error?: ErrorMessageType;
}

function changeQuestion(state: QuestionReducerState, { utr, utrv, surveyData, user }: ChangeQuestionProps) {

  const { displayCheckbox, originalValueData } = getDisplayCheckboxAndOriginalValueData({
    valueType: utr.getValueType(),
    utrv,
  });

  const valueData = JSON.parse(JSON.stringify(originalValueData));

  const update: Partial<QuestionReducerState> = {
    valueData: valueData ?? {},
    value: getInputValue(utrv),
    unit: getUnitCode(utrv, utr, surveyData.unitConfig),
    numberScale: getNumberScale(utrv, utr, surveyData.unitConfig),
    displayCheckbox,
    canContribute: UtrvPermissions.canContribute(utrv, surveyData, user),
    canVerify: UtrvPermissions.canVerify(utrv, surveyData, user),
    alternativeCode: utr.getType(),
    alternativeCodeOverride: '',
  };

  if (state.saveId === utr.getId()) {
    update.message = state.message;
  }

  // Set up table
  const tableConfiguration: ValueTable | undefined = getTableConfiguration(utr);
  if (tableConfiguration) {
    update.table = tableDataToView(valueData);

    // [GU-5815] We should initialise single row validation for fresh change question,
    // to ensure that submit button is disabled when the table have validation errors.
    if (isSingleRowTableType(utr) && update.table.rows.length === 1) {
      const firstRow = update.table.rows[0];
      update.inputMessage = generateDecimalErrorMessage({
        utr: utr,
        input: Object.fromEntries(firstRow.data.map((c) => [c.code, c.value])),
      });
    }
  }

  return {
    ...state,
    ...getInitialState(),
    ...update,
  };
}

export function getExistingFiles(utrvHistory?: UtrvHistory): ExistingEvidenceFile[] {
  if (!utrvHistory) {
    return [];
  }

  const existingFiles: ExistingEvidenceFile[] = [];
  const { stakeholderHistory, verifierHistory, documents } = utrvHistory.latestHistory;

  [
    stakeholderHistory,
    verifierHistory,
  ]
    .filter((h) => h?.evidence && h?.evidence.length > 0)
    .forEach(h => {
      if (h?.evidence) {
        for (const evidenceId of h.evidence) {
          const doc = documents.find((d) => d._id === evidenceId);
          if (doc) {
            doc.status = h.action;
            existingFiles.push(doc);
          }
        }
      }
    });

  return existingFiles;
}

function loadDetails(state: QuestionReducerState, updateData: ReducerAction['data']) {

  const existingFiles = updateData && 'data' in updateData ? getExistingFiles(updateData.data) : [];

  return {
    ...state,
    details: {
      ...state.details,
      ...updateData
    },
    existingFiles,
  };
}

function toggleExistingFile(state: QuestionReducerState, data: any) {

  const { file } = data;
  const existingFiles = state.existingFiles;
  const index = existingFiles.findIndex(({ _id }) => _id === file._id);

  if (!existingFiles[index]) {
    return state;
  }

  const newFiles = [...existingFiles];
  newFiles[index].isDeleted = !newFiles[index].isDeleted;

  return { ...state, existingFiles: newFiles };
}

function updateTable(state: QuestionReducerState, data?: Partial<TableDataInfo>, error?: {[key: string]: string | undefined }) {
  if (!data) {
    return state;
  }

  return {
    ...state,
    inputMessage: error,
    table: {
      ...state.table,
      ...data
    }
  };
}

function questionReducer(state: QuestionReducerState, action: ReducerAction): QuestionReducerState {
  switch (action.type) {
    case useQuestionForm.types.handleComments:
      return { ...state, comments: action.comments || '', editorState: action.editorState }
    case useQuestionForm.types.update:
      return { ...state, ...action.data }
    case useQuestionForm.types.reset:
      return { ...getInitialState(), ...assuranceState }
    case useQuestionForm.types.changeQuestion:
      return action.questionChange ? changeQuestion(state, action.questionChange) : state;
    case useQuestionForm.types.updateExistingFiles:
      return toggleExistingFile(state, action.data);
    case useQuestionForm.types.setDetails:
      return loadDetails(state, action.data);
    case useQuestionForm.types.updateTable:
      return updateTable(state, action.table, action.error);
    default: {
      throw new Error(`Unhandled type: ${action.type}`)
    }
  }
}

export function useQuestionForm(initObj = {}, initializer?: any) {
  const [state, dispatch] = React.useReducer(
    questionReducer,
    { ...getInitialState(), ...assuranceState },
    initializer
  );

  // TODO: Update types here. Tried to use ReducerAction['data'] but it doesn't include 'files' and 'existingFiles'
  const update = useCallback((data: any) => dispatch({ type: useQuestionForm.types.update, data }), []);
  const setDetails = useCallback((data: any) => dispatch({ type: useQuestionForm.types.setDetails, data }), []);
  const resetState = useCallback(() => dispatch({ type: useQuestionForm.types.reset }), []);
  const changeQuestion = useCallback((data: ChangeQuestionProps) => dispatch({ type: useQuestionForm.types.changeQuestion, questionChange: data }), []);
  const updateTable = useCallback(
    (table: Partial<TableDataInfo>, error?: ErrorMessageType) =>
      dispatch({
        type: useQuestionForm.types.updateTable,
        table,
        error,
      }),
    []
  );
  const toggleExistingEvidence = useCallback((data: any) => dispatch({
    type: useQuestionForm.types.updateExistingFiles,
    data
  }), []);

  return {
    state,
    update,
    resetState,
    changeQuestion,
    updateTable,
    toggleExistingEvidence,
    setDetails,
    handleComments: (comments: string, editorState?: EditorState) => dispatch({
      type: useQuestionForm.types.handleComments,
      comments,
      editorState,
    }),
    handleEvidenceLinkAdd: (link: EvidenceLink, isPublic: boolean) => {
      update({ files: handleEvidenceLinkAdd(state.files, link, isPublic) });
    },
    handleFilesAdded: (addFiles: File[]) => resolveFiles(
      state.files,
      addFiles,
      (newFiles: EvidenceFile[]) => update({ files: newFiles })
    ),
    setFiles: (addFiles: EvidenceFile[]) => {
      const newFiles = state.files.filter(file => !('_id' in file));
      update({ files: [...newFiles, ...addFiles] })
    },
    handleFileRemoved: (index: number) => removeFiles(
      state.files,
      index,
      (files: EvidenceFile[]) => update({ files })
    ),
    handleFileDescriptionAdd: ({ path, description, isUpdate }: UpdateDescriptionParams) => {
      if (isUpdate) {
        update({ existingFiles: handleFileDescriptionAdd({ evidenceFiles: state.existingFiles, path, description }) });
      }
      update({ files: handleFileDescriptionAdd({ evidenceFiles: state.files, path, description }) });
    },
    handleAddToLibrary: (file: NewEvidenceFile
    ) =>
      update({ files: toggleAddFileToLibrary({ evidenceFiles: state.files, path: file.file.path }) }),
  }
}

useQuestionForm.types = {
  handleComments: 'handleComments',
  update: 'update',
  reset: 'reset',
  changeQuestion: 'changeQuestion',
  updateExistingFiles: 'updateExistingFilesOrSo',
  setDetails: 'setDetails',
  updateTable: 'updateTable',
}
