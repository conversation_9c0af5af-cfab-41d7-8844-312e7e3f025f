/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { AddToLibraryFn, EvidenceFile, HandleFileDescriptionFn } from '@g17eco/types/file';
import { EvidenceContainer } from './EvidenceContainer';
import './FileListContainer.scss';

export interface FileListContainerProps {
  files: EvidenceFile[];
  title?: string;
  existingFiles?: EvidenceFile[];
  allowToggle?: boolean;
  handleFileRemoved?: (i: number) => void;
  handleExistingFileRemoved?: (data: any) => void;
  handleFileDescriptionAdd?: HandleFileDescriptionFn;
  handleAddToLibrary?: AddToLibraryFn;
  details?: {
    loaded: boolean,
    data: any
  };
  saving?: boolean;
  metadata: {
    initiativeId: string;
  }
}

export const FileListContainer = (props: FileListContainerProps) => {
  const {
    allowToggle,
    handleExistingFileRemoved,
    handleFileRemoved,
    title,
    metadata,
  } = props;

  const sizeClass = 'col-12';

  return (
    <div className='fileListContainer my-2'>
      <div className={`fileList current-files ${sizeClass}`}>
        {props.files.length > 0 && title ? <h5>{title}</h5> : <></>}
        <EvidenceContainer
          {...props}
          allowToggle={true}
          isLoaded={true}
          toggleFile={handleFileRemoved}
          saving={!!props.saving}
          metadata={metadata}
        />
      </div>
      <div className={`fileList existingFiles ${sizeClass}`}>
        {props.existingFiles && props.existingFiles.length> 0 ? <h5>Existing evidence</h5> : <></>}
        <EvidenceContainer
          files={props.existingFiles ?? []}
          toggleFile={(i, file) => handleExistingFileRemoved?.({ file })}
          allowToggle={allowToggle ?? false}
          isLoaded={true}
          saving={!!props.saving}
          handleFileDescriptionAdd={props.handleFileDescriptionAdd}
          metadata={metadata}
        />
      </div>
    </div>
  );
};
