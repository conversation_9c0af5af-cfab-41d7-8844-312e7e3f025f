import { Input } from 'reactstrap';
import { isNewEvidence } from '@utils/survey/evidence';
import { EvidenceFile, AddToLibraryFn } from '@g17eco/types/file';

export const AddToLibraryCheckbox = (props: { file: EvidenceFile; handleAddToLibrary?: AddToLibraryFn }) => {
  const { file, handleAddToLibrary } = props;

  if (!isNewEvidence(file) || !handleAddToLibrary) {
    return null;
  }

  return (
    <div className='d-flex gap-1 mt-2' data-testid='add-to-library-checkbox'>
      <Input type='checkbox' onChange={() => handleAddToLibrary(file)} />
      <span>Also add to Document Library</span>
    </div>
  );
};
