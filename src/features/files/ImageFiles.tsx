/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React from 'react';
import { Button } from 'reactstrap';
import { ToggleIcon } from './ToggleIcon';
import './ImageFiles.scss';
import NumberFormat from '../../utils/number-format';
import config from '../../config';
import { FileDescription } from './FileDescription';
import { AddToLibraryFn, EvidenceFile, HandleFileDescriptionFn } from '@g17eco/types/file';
import { CLOSED_PHOTO_INDEX, G17Lightbox } from '@g17eco/molecules/lightbox';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { AddToLibraryCheckbox } from './AddToLibraryCheckbox';

interface ImageFilesProps {
  photos: EvidenceFile[];
  toggleFile?: (i: number, file: EvidenceFile) => void;
  allowToggle?: boolean;
  zIndex?: number;
  handleFileDescriptionAdd?: HandleFileDescriptionFn;
  isReadOnly: boolean;
  metadata: {
    initiativeId: string;
  };
  handleAddToLibrary?: AddToLibraryFn;
}

const getURL = (file: EvidenceFile) => {
  if ('metadata' in file) {
    return file.url;
  }
  if (file.file.preview) {
    return URL.createObjectURL(file.file);
  }
  return '';
};

const StatusIcon = ({ file }: { file: EvidenceFile }) => {
  const lat = 'metadata' in file ? file.metadata?.exif?.GPSLatitude : file.file.Latitude;
  const lon = 'metadata' in file ? file.metadata?.exif?.GPSLongitude : file.file.Longitude;

  if (!lat || !lon) {
    return (
      <SimpleTooltip
        className='text-center'
        placement='left'
        text='No GPS coordinates in image. You can still use as evidence, but if possible please use images with valid geo-coordinates'
      >
        <i className='gps-status-icon text-ThemeWarningMedium fas fa-exclamation-triangle' />
      </SimpleTooltip>
    );
  }
  return (
    <SimpleTooltip className='text-center' placement='left' text='This image has confirmed GPS data attached.'>
      <i className='gps-status-icon text-ThemeSuccessMedium fas fa-check-circle' />
    </SimpleTooltip>
  );
};

export const ImageFiles = (props: ImageFilesProps) => {
  const { photos, toggleFile, allowToggle, isReadOnly, handleFileDescriptionAdd, metadata, handleAddToLibrary } = props;

  const [photoIndex, setPhotoIndex] = React.useState(-1);
  const [displayGeoModal, setDisplayGeoModal] = React.useState(false);

  const toggleGeoModal = (index: number) => {
    setDisplayGeoModal(!displayGeoModal);
    setPhotoIndex(index);
  };

  const handleLightBox = (e: React.MouseEvent<HTMLDivElement, MouseEvent>, idx: number) => {
    e.preventDefault();
    setPhotoIndex(idx);
  };

  const renderGoogleMap = (file: EvidenceFile, index: number) => {
    const lat = 'metadata' in file ? file.metadata?.exif?.GPSLatitude : file.file.Latitude;
    const lon = 'metadata' in file ? file.metadata?.exif?.GPSLongitude : file.file.Longitude;

    if (!lat || !lon) {
      return <Button disabled={true}>No GPS location detected</Button>;
    }

    if (!displayGeoModal) {
      return (
        <Button onClick={() => toggleGeoModal(index)}>
          <i className='fas fa-lg fa-map-marker-alt mr-1' />
          View Location Map
        </Button>
      );
    }

    const latLong = `${lat},${lon}`;
    return (
      <div className='mapContainer'>
        <Button className='closeButton' color='danger' onClick={() => toggleGeoModal(index)}>
          <i className='fa fas fa-times' />
        </Button>
        <img
          alt='Geo location'
          src={`https://maps.googleapis.com/maps/api/staticmap?zoom=16&key=${config.googleMapsKey}&size=600x300&markers=${latLong}`}
        />
      </div>
    );
  };

  const slides = photos.map((photo, idx) => ({
    src: getURL(photo),
    description: renderGoogleMap(photos[idx], idx),
  }));

  return (
    <div className='container'>
      <div className='row mediaTileContainer gap-2 flex-column'>
        {photos.map((photo, idx) => {
          const isDocumentLibrary = 'ownerId' in photo && photo.ownerId === metadata.initiativeId;
          return (
            <div key={'photo-' + idx} className='toggleButton mediaTile align-self-center d-flex gap-1'>
              <div className='d-flex flex-column'>
                {toggleFile ? (
                  <ToggleIcon i={idx} file={photo} allowToggle={allowToggle} toggleFile={toggleFile} />
                ) : (
                  <></>
                )}
                <StatusIcon file={photo} />
              </div>
              <div className='d-flex flex-column flex-fill'>
                <div className='d-flex gap-1'>
                  <div className='img-thumbnail toggleButton' onClick={(e) => handleLightBox(e, idx)}>
                    <div className='mediaTileImage' style={{ backgroundImage: 'url(' + getURL(photo) + ')' }}></div>
                    <div>
                      <NumberFormat
                        value={('metadata' in photo ? photo.size : photo.file.size) / 1024}
                        decimalPlaces={1}
                        suffix='kb'
                      />
                    </div>
                  </div>
                  {isDocumentLibrary ? (
                    <div className='text-sm text-ThemTextMedium'>Document library: Page references not supported yet</div>
                  ) : (
                    <FileDescription
                      handleFileDescriptionAdd={handleFileDescriptionAdd}
                      photo={photo}
                      isReadOnly={isReadOnly}
                      className='align-items-start'
                    />
                  )}
                </div>
                <AddToLibraryCheckbox file={photo} handleAddToLibrary={handleAddToLibrary} />
              </div>
            </div>
          );
        })}
      </div>
      <G17Lightbox
        on={{
          view: ({ index }: { index: number }) => {
            setDisplayGeoModal(false);
            setPhotoIndex(index);
          },
        }}
        hasCaptions
        slides={slides}
        photoIndex={photoIndex}
        handleReset={() => setPhotoIndex(CLOSED_PHOTO_INDEX)}
      />
    </div>
  );
};
