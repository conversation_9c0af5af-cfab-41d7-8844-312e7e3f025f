import { useState } from 'react';
import { DocumentItem } from '@g17eco/types/document';
import { Button, Form, FormGroup, Input, Label, Modal, ModalBody, ModalHeader } from 'reactstrap';
import FileGrid from '@features/files/file-grid';

export enum DocumentViewMode {
  View = 'view',
  Edit = 'edit',
  Delete = 'delete',
}

interface DocumentModalProps {
  isOpen: boolean;
  mode: DocumentViewMode;
  document: DocumentItem;
  toggle: () => void;
  handleUpdate: (data: DocumentItem) => void;
  handleDelete: (documentId: string) => void;
}

export const DocumentModal = ({ isOpen, mode, document, toggle, handleUpdate, handleDelete }: DocumentModalProps) => {
  const [form, setForm] = useState<Pick<DocumentItem, 'title' | 'description'>>({
    title: document.title,
    description: document.description,
  });

  const updateForm = (e: React.ChangeEvent<{ name: string; value: string | undefined }>) => {
    const { name, value } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const updateDocument = () => {
    handleUpdate({ ...document, ...form });
    toggle();
  };

  const renderContent = () => {
    switch (mode) {
      case DocumentViewMode.View:
        return (
          <div className='d-flex flex-column'>
            <FormGroup>
              <Label className='strong' for='title'>
                File name
              </Label>
              <FileGrid files={[document]} options={{ showTitle: true }} />
            </FormGroup>
            {document.description ? (
              <FormGroup>
                <Label className='strong' for='description'>
                  Description
                </Label>
                <div>{document.description}</div>
              </FormGroup>
            ) : null}
          </div>
        );
      case DocumentViewMode.Edit:
        return (
          <Form>
            <FormGroup>
              <Label className='strong' for='title'>
                Title
              </Label>
              <Input id='title' type='text' name='title' onChange={updateForm} value={form.title ?? ''} />
            </FormGroup>
            <FormGroup>
              <Label className='strong' for='description'>
                Description
              </Label>
              <Input
                id='description'
                type='text'
                name='description'
                onChange={updateForm}
                value={form.description ?? ''}
              />
            </FormGroup>
            <div className='d-flex justify-content-end'>
              <Button color='link-secondary' className='mr-3' onClick={toggle}>
                Cancel
              </Button>
              <Button color='primary' onClick={updateDocument}>
                Submit
              </Button>
            </div>
          </Form>
        );
      case DocumentViewMode.Delete:
        return (
          <div className='d-flex flex-column'>
            <p>This will permanently delete the item. Continue?</p>
            <div className='text-right mt-3'>
              <Button color='link-secondary' onClick={toggle} className='mr-3'>
                Cancel
              </Button>
              <Button color='danger' className='ml-3' onClick={() => handleDelete(document._id)}>
                Delete
              </Button>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const getHeader = () => {
    switch (mode) {
      case DocumentViewMode.Edit:
        return 'Edit Document Details';
      case DocumentViewMode.View:
        return 'Document Details';
      case DocumentViewMode.Delete:
        return 'Delete Document';
      default:
        return null;
    }
  };

  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>{getHeader()}</ModalHeader>
      <ModalBody>{renderContent()}</ModalBody>
    </Modal>
  );
};
