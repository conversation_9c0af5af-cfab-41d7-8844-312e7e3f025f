/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { useEffect, useState } from 'react';
import { Button, Modal, <PERSON>dalB<PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, ModalHeader } from 'reactstrap';
import { DownloadType, HandleDownload, ScopeTotals, DownloadSettingPropType } from '@g17eco/types/download';
import { DownloadQuestionSettings } from './DownloadQuestionSettings';
import { generatingReportText } from './DownloadSettingModal';
import { DownloadSettingOptions } from './DownloadSettingOptions';
import { PackSelect } from './partials/PackSelect';
import { Totals } from './partials/Totals';
import { getDownLoadOptions } from './util/downloadReportHandler';
import { PackOption } from '../../constants/standards-frameworks';
import { useAppSelector } from '../../reducers';
import { FeaturePermissions } from '../../services/permissions/FeaturePermissions';
import { generateUrl } from '@routes/util';
import { useHistory } from 'react-router-dom';
import { ROUTES } from '@constants/routes';
import { useGetInitiativeUniversalTrackersQuery } from '@api/initiative-universal-trackers';
import { skipToken } from '@reduxjs/toolkit/query';
import { BasicAlert } from '@g17eco/molecules/alert';
import { getRootConfig } from '@selectors/globalData';

type DownloadOthersSettingPropType = Pick<
  DownloadSettingPropType,
  | 'isOpen'
  | 'isDisabled'
  | 'title'
  | 'downloadSettings'
  | 'disabledOptions'
  | 'handleClose'
  | 'handleChange'
  | 'onDownload'
  | 'defaultDownloadOptions'
  | 'initiativeId'
> & {
  getTotals?: (type: string) => ScopeTotals;
  packTitle?: string;
  defaultPackCode?: string;
  packOptions: PackOption[];
};

export const DownloadOthersSettingModal = (props: DownloadOthersSettingPropType) => {
  const {
    isOpen,
    isDisabled = false,
    title,
    downloadSettings,
    disabledOptions = ['word'],
    handleClose,
    handleChange,
    onDownload,
    getTotals,
    packTitle,
    defaultPackCode,
    packOptions,
    defaultDownloadOptions,
    initiativeId
  } = props;

  const [isGeneratingReport, setGeneratingReport] = useState(false);
  const [packCode, setPackCode] = useState<string>('');
  const totals = getTotals?.(packCode);
  const rootConfig = useAppSelector(getRootConfig);
  const canUsePPTX = FeaturePermissions.canAccessPPTXReportAI(rootConfig);
  const history = useHistory();
  const { data: initiativeUtrs = [] } = useGetInitiativeUniversalTrackersQuery(initiativeId ?? skipToken);

  useEffect(() => {
    if (defaultPackCode) {
      setPackCode(defaultPackCode);
    }
  }, [defaultPackCode, isOpen]);

  const goToPPTXReportsPage = () => {
    if (!initiativeId) {
      return;
    }

    history.push(generateUrl(ROUTES.DOWNLOADS_PPTX, { initiativeId }));
  };


  const handleDownloadReport: HandleDownload = async (type, config) => {
    if (type === DownloadType.Pptx) {
      goToPPTXReportsPage();
      return;
    }
    setGeneratingReport(true);
    await onDownload(type, {
      ...config,
      packCode,
      scopeType: packOptions.find((option) => option.value === packCode)?.scopeType,
    });
    handleToggle();
    setGeneratingReport(false);
  };

  const handleToggle = () => {
    handleClose();
    setPackCode('');
  }

  const options = getDownLoadOptions({
    code: packCode,
    disabledOptions,
    handleDownloadReport,
    canUsePPTX,
  });

  return (
    <Modal className='download-setting__modal' isOpen={isOpen} toggle={handleToggle} backdrop='static'>
      <ModalHeader toggle={handleToggle}>{title}</ModalHeader>
      <ModalBody>
        {isGeneratingReport ? (
          <BasicAlert type='info'>{generatingReportText}</BasicAlert>
        ) : (
          <>
            <h5 className='fw-bold mb-3'>Download Settings</h5>
            {packTitle ? <h6 className='text-ThemeTextDark'>{packTitle}</h6> : null}
            <div className='mb-3'>
              <PackSelect pack={packCode} packOptions={packOptions} handleSelectPack={(pack: string) => setPackCode(pack)} />
              <Totals totals={totals} className='py-2' />
            </div>
            <DownloadQuestionSettings
              code={packCode}
              isDisabled={!packCode}
              totals={totals}
              downloadSettings={downloadSettings}
              handleChange={handleChange}
              hasInitiativeUtrs={initiativeUtrs.length > 0}
              defaultDownloadOptions={defaultDownloadOptions}
            />
            <DownloadSettingOptions
              options={options}
              isDisabled={isDisabled || !packCode}
            />
          </>
        )}
      </ModalBody>
      <ModalFooter className='text-right'>
        <Button outline size='sm' className='mr-2 pl-4 pr-4' onClick={handleToggle}>
          Close
        </Button>
      </ModalFooter>
    </Modal>
  );
};
