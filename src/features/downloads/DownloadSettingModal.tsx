/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { Button, Modal, ModalBody, <PERSON>dal<PERSON>ooter, ModalHeader } from 'reactstrap';
import { DownloadType, HandleDownload, DownloadSettingPropType } from '@g17eco/types/download';
import { useHistory } from 'react-router-dom';
import { getDownLoadOptions } from './util/downloadReportHandler';
import { DownloadQuestionSettings } from './DownloadQuestionSettings';
import { DownloadSettingOptions } from './DownloadSettingOptions';
import { ScopeSelect } from './ScopeSelect';
import { useAppSelector } from '../../reducers';
import { FeaturePermissions } from '../../services/permissions/FeaturePermissions';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { useGetInitiativeUniversalTrackersQuery } from '@api/initiative-universal-trackers';
import { skipToken } from '@reduxjs/toolkit/query';
import { BasicAlert } from '@g17eco/molecules/alert';
import { getRootConfig } from '@selectors/globalData';

export const generatingReportText = 'Generating report...';

export const DownloadSettingModal = (props: DownloadSettingPropType) => {
  const {
    isOpen,
    isDisabled = false,
    title,
    downloadSettings,
    handleClose,
    handleChange,
    onDownload,
    code,
    requiredScope,
    totals,
    disabledOptions = [],
    titleOptions,
    defaultDownloadOptions = {},
    portfolioId,
    initiativeId
  } = props;
  const [isGeneratingReport, setGeneratingReport] = useState(false);
  const [message, setMessage] = useState('');
  const rootConfig = useAppSelector(getRootConfig);
  const canUsePPTX = FeaturePermissions.canAccessPPTXReportAI(rootConfig);
  const history = useHistory();

  const { data: initiativeUtrs = [] } = useGetInitiativeUniversalTrackersQuery(initiativeId ?? skipToken);
  const goToPPTXReportsPage = () => {
    if (!initiativeId) {
      return;
    }

    history.push(generateUrl(ROUTES.DOWNLOADS_PPTX, { initiativeId }));
  };

  const handleDownloadReport: HandleDownload = async (type, config) => {
    setMessage('')
    if (type === DownloadType.Pptx) {
      goToPPTXReportsPage();
      return;
    }
    setGeneratingReport(true);
    try {
      await onDownload(type, config);
      handleClose();
    } catch (e) {
      setMessage(e.message)
    } finally {
      setGeneratingReport(false);
    }
  }

  const options = getDownLoadOptions({
    code,
    titleOptions,
    disabledOptions,
    handleDownloadReport,
    canUsePPTX
  });

  const selectedGroups = downloadSettings?.selectedScopes ?? [];

  // We have scopes, but nothing is selected
  const isEmptyScope = selectedGroups.length > 0 && selectedGroups.every(item => !item.checked);

  return (
    <Modal className='download-setting__modal' isOpen={isOpen} toggle={handleClose} backdrop='static'>
      <ModalHeader toggle={handleClose}>{title}</ModalHeader>
      <ModalBody>
        <BasicAlert key={'error'} type={'danger'}>{message}</BasicAlert>
        {isGeneratingReport ? (
          <BasicAlert type='info'>{generatingReportText}</BasicAlert>
        ) : (
          <>
            <h5 className='fw-bold mb-3'>Download Settings</h5>
            <DownloadQuestionSettings
              code={code}
              isDisabled={isEmptyScope}
              totals={totals}
              isPortfolioTracker={!!portfolioId}
              downloadSettings={downloadSettings}
              handleChange={handleChange}
              defaultDownloadOptions={defaultDownloadOptions}
              hasInitiativeUtrs={initiativeUtrs.length > 0}
            />
            <ScopeSelect
              hidden={!downloadSettings || code === 'sdg'}
              requiredScope={requiredScope}
              isEmptyScope={isEmptyScope}
              selectedGroups={selectedGroups}
              titleOptions={titleOptions}
              handleChange={handleChange}
            />

            <DownloadSettingOptions
              options={options}
              isDisabled={isDisabled || isEmptyScope}
            />
          </>
        )}
      </ModalBody>
      <ModalFooter className='text-right'>
        <Button outline size='sm' className='mr-2 pl-4 pr-4' onClick={handleClose}>
          Close
        </Button>
      </ModalFooter>
    </Modal>
  );
};
