import { BulkActionUtr } from '@g17eco/types/survey-question-list';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { AiProcessingValue, MetricOverrideType, UTRV_CONFIG_CODES, UtrvConfigCode, UtrvConfigValue } from './types';
import { QUESTION } from '@constants/terminology';

const defaultForm: MetricOverrideType = {
  verificationRequired: UtrvConfigValue.Default,
  evidenceRequired: UtrvConfigValue.Default,
  noteRequired: UtrvConfigValue.Default,
  isPrivate: UtrvConfigValue.Default,
  aiProcessing: AiProcessingValue.Include,
};

export const getInitialForm = ({
  initiativeUtrMap,
  selectedQuestions,
}: {
  initiativeUtrMap: Map<string, InitiativeUniversalTracker>;
  selectedQuestions: BulkActionUtr[];
}) => {
  if (initiativeUtrMap.size === 0) {
    return defaultForm;
  }

  return selectedQuestions.reduce(
    (acc, question) => {
      const initiativeUtr = initiativeUtrMap.get(question._id);
      if (!initiativeUtr) {
        return acc;
      }

      UTRV_CONFIG_CODES.forEach((code) => {
        if (acc[code] !== UtrvConfigValue.Default) {
          return;
        }
        if (initiativeUtr.utrvConfig?.[code]) {
          acc[code] = initiativeUtr.utrvConfig[code];
        }
      });

      if (acc.aiProcessing !== AiProcessingValue.Include) {
        return acc;
      }
      if (initiativeUtr.aiProcessing) {
        acc.aiProcessing = initiativeUtr.aiProcessing;
      }
      return acc;
    },
    { ...defaultForm },
  );
};

export const getLabelByCode = (code: keyof MetricOverrideType) => {
  switch (code) {
    case 'verificationRequired':
      return 'Verification';
    case 'evidenceRequired':
      return 'Evidence';
    case 'noteRequired':
      return 'Further explanation/notes';
    case 'isPrivate':
      return `${QUESTION.CAPITALIZED_SINGULAR} privacy`;
    case 'aiProcessing':
      return 'AI Processing';
    default:
      return '';
  }
};

export const getAIProcessingOptions = () => [
  {
    label: 'Include in AI processing',
    value: AiProcessingValue.Include,
  },
  {
    label: 'Always exclude from AI processing',
    value: AiProcessingValue.Exclude,
  },
];

export const getUtrvConfigOptions = (code: UtrvConfigCode) => {
  return [
    {
      label: 'Use report default settings',
      value: UtrvConfigValue.Default,
    },
    {
      label: code === 'isPrivate' ? `${QUESTION.CAPITALIZED_PLURAL} always public` : `${getLabelByCode(code)} always optional`,
      value: UtrvConfigValue.Optional,
    },
    {
      label: code === 'isPrivate' ? `${QUESTION.CAPITALIZED_PLURAL} always private` : `${getLabelByCode(code)} always required`,
      value: UtrvConfigValue.Required,
    },
  ];
};

export const getOptionsByCode = (code: keyof MetricOverrideType) => {
  switch (code) {
    case 'aiProcessing':
      return getAIProcessingOptions();
    default:
      return getUtrvConfigOptions(code);
  }
};

export const hasDataChanged = (defaultValue: MetricOverrideType, value: MetricOverrideType) => {
  return Object.keys(defaultValue).some(
    (code) => defaultValue[code as keyof MetricOverrideType] !== value[code as keyof MetricOverrideType],
  );
};
