export enum UtrvConfigValue {
  Default = 'default',
  Optional = 'optional',
  Required = 'required',
}

export enum AiProcessingValue {
  Include = 'include',
  Exclude = 'exclude',
}

// initiativeUtr's isPrivate: 'optional' -> utr's isPrivate: false
// initiativeUtr's isPrivate: 'required' -> utr's isPrivate: true
export const UTRV_CONFIG_CODES = ['verificationRequired', 'evidenceRequired', 'noteRequired', 'isPrivate'] as const;

export type UtrvConfigCode = (typeof UTRV_CONFIG_CODES)[number];

export const METRIC_OVERRIDE_CODES = [...UTRV_CONFIG_CODES, 'aiProcessing'] as const;

export interface MetricOverrideType extends Record<UtrvConfigCode, UtrvConfigValue> {
  aiProcessing: AiProcessingValue;
}

export const isUtrvConfigCode = (code: string): code is UtrvConfigCode => {
  return UTRV_CONFIG_CODES.includes(code as UtrvConfigCode);
};
