import { describe } from 'vitest';
import { getLabelByCode, getOptionsByCode, getInitialForm, hasDataChanged } from './utils';
import { AiProcessingValue, UtrvConfigValue } from './types';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';

describe('getLabelByCode', () => {
  it('should return correct labels for all config codes', () => {
    expect(getLabelByCode('verificationRequired')).toBe('Verification');
    expect(getLabelByCode('evidenceRequired')).toBe('Evidence');
    expect(getLabelByCode('noteRequired')).toBe('Further explanation/notes');
    expect(getLabelByCode('isPrivate')).toBe('Metric privacy');
  });

  it('should return empty string for unknown codes', () => {
    expect(getLabelByCode('unknown' as any)).toBe('');
  });
});

describe('getOptionsByCode', () => {
  it('should return correct options for verificationRequired', () => {
    const options = getOptionsByCode('verificationRequired');

    expect(options).toHaveLength(3);
    expect(options[0]).toEqual({
      label: 'Use report default settings',
      value: UtrvConfigValue.Default,
    });
    expect(options[1]).toEqual({
      label: 'Verification always optional',
      value: UtrvConfigValue.Optional,
    });
    expect(options[2]).toEqual({
      label: 'Verification always required',
      value: UtrvConfigValue.Required,
    });
  });

  it('should return correct options for isPrivate', () => {
    const options = getOptionsByCode('isPrivate');

    expect(options).toHaveLength(3);
    expect(options[1]).toEqual({
      label: 'Metrics always public',
      value: UtrvConfigValue.Optional,
    });
    expect(options[2]).toEqual({
      label: 'Metrics always private',
      value: UtrvConfigValue.Required,
    });
  });
});

describe('getInitialForm', () => {
  it('should return default config when no initiative UTRs exist', () => {
    const result = getInitialForm({
      initiativeUtrMap: new Map(),
      selectedQuestions: [{ _id: '1' }] as BulkActionUtr[],
    });

    expect(result).toEqual({
      verificationRequired: UtrvConfigValue.Default,
      evidenceRequired: UtrvConfigValue.Default,
      noteRequired: UtrvConfigValue.Default,
      isPrivate: UtrvConfigValue.Default,
      aiProcessing: AiProcessingValue.Include,
    });
  });

  it('should merge config from initiative UTRs', () => {
    const initiativeUtr: InitiativeUniversalTracker = {
      _id: '1',
      initiativeId: 'init1',
      universalTrackerId: 'utr1',
      utrvConfig: {
        verificationRequired: UtrvConfigValue.Required,
        evidenceRequired: UtrvConfigValue.Optional,
        noteRequired: UtrvConfigValue.Default,
        isPrivate: UtrvConfigValue.Default,
      },
      aiProcessing: AiProcessingValue.Exclude,
    } as InitiativeUniversalTracker;

    const initiativeUtrMap = new Map([['1', initiativeUtr]]);
    const selectedQuestions = [{ _id: '1' }] as BulkActionUtr[];

    const result = getInitialForm({
      initiativeUtrMap,
      selectedQuestions,
    });

    expect(result).toEqual({
      verificationRequired: UtrvConfigValue.Required,
      evidenceRequired: UtrvConfigValue.Optional,
      noteRequired: UtrvConfigValue.Default,
      isPrivate: UtrvConfigValue.Default,
      aiProcessing: AiProcessingValue.Exclude,
    });
  });

  it('should prioritize non-default values when merging multiple questions', () => {
    const initiativeUtr1: InitiativeUniversalTracker = {
      _id: '1',
      initiativeId: 'init1',
      universalTrackerId: 'utr1',
      utrvConfig: {
        verificationRequired: UtrvConfigValue.Default,
        evidenceRequired: UtrvConfigValue.Required,
        noteRequired: UtrvConfigValue.Default,
        isPrivate: UtrvConfigValue.Default,
      },
    } as InitiativeUniversalTracker;

    const initiativeUtr2: InitiativeUniversalTracker = {
      _id: '2',
      initiativeId: 'init1',
      universalTrackerId: 'utr2',
      utrvConfig: {
        verificationRequired: UtrvConfigValue.Optional,
        evidenceRequired: UtrvConfigValue.Default,
        noteRequired: UtrvConfigValue.Required,
        isPrivate: UtrvConfigValue.Default,
      },
      aiProcessing: AiProcessingValue.Exclude,
    } as InitiativeUniversalTracker;

    const initiativeUtrMap = new Map([
      ['1', initiativeUtr1],
      ['2', initiativeUtr2],
    ]);
    const selectedQuestions = [{ _id: '1' }, { _id: '2' }] as BulkActionUtr[];

    const result = getInitialForm({
      initiativeUtrMap,
      selectedQuestions,
    });

    expect(result).toEqual({
      verificationRequired: UtrvConfigValue.Optional,
      evidenceRequired: UtrvConfigValue.Required,
      noteRequired: UtrvConfigValue.Required,
      isPrivate: UtrvConfigValue.Default,
      aiProcessing: AiProcessingValue.Exclude,
    });
  });
});

describe('hasDataChanged', () => {
  const initialForm = {
    verificationRequired: UtrvConfigValue.Default,
    evidenceRequired: UtrvConfigValue.Default,
    noteRequired: UtrvConfigValue.Default,
    isPrivate: UtrvConfigValue.Default,
    aiProcessing: AiProcessingValue.Include,
  };

  it('should return false when no data has changed', () => {
    const result = hasDataChanged(initialForm, initialForm);
    expect(result).toBe(false);
  });

  it('should return true when UTRV config has changed', () => {
    const currentForm = {
      ...initialForm,
      verificationRequired: UtrvConfigValue.Required,
    };

    const result = hasDataChanged(initialForm, currentForm);
    expect(result).toBe(true);
  });
});
