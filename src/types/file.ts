/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { DocumentSubType, HistoryDocument } from './document';

export type FileTypes = 'file' | 'link';

export interface FileWithPreview extends File {
  preview?: string;
  Latitude?: string | number;
  Longitude?: string | number;
  exifdata?: {
    GPSLongitudeRef: string;
    GPSLatitudeRef: string;
    GPSLongitude: number[];
    GPSLatitude: number[];
  };
  path?: string;
  saveToLibrary?: boolean;
}

export interface ExistingEvidenceFile extends HistoryDocument {
  status: string; // E.g: 'updated'
  type: FileTypes;
  link?: string;
  isDeleted?: boolean;
  isUpdated?: boolean;
  ownerSubType?: DocumentSubType;
  ownerId?: string;
}

export type EvidenceFile = ExistingEvidenceFile | NewEvidenceFile;

export interface NewEvidenceFile {
  description: string;
  title: string;
  file: FileWithPreview;
  type: FileTypes;
  link?: string;
  saveToLibrary?: boolean;
}

export type TKeyValue = {
  [key: string]: any;
};

export interface UpdateDescriptionParams {
  path?: string;
  description: string;
  isUpdate: boolean;
}

export type HandleFileDescriptionFn = (params: UpdateDescriptionParams) => void;
export type AddToLibraryFn = (file: NewEvidenceFile) => void;
