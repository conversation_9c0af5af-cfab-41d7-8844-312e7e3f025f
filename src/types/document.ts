/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

export interface DownloadDocument {
  url: string;
}

export interface InitiativeDocument {
  _id: string;
  title: string;
  description: string;
  ownerId: string;
  url: string;
  size: number;
  created: Date;
  metadata: {
    name: string;
    mimetype: string;
    extension: string;
  };
}

export type DocumentItem = HistoryDocument &
  InitiativeDocument & {
    ownerSubType?: DocumentSubType;
  };

export interface Metadata {
  name: string;
  mimetype: string;
  extension: string;
  exif?: any;
}

export interface HistoryDocument {
  _id: string;
  metadata: Metadata;
  public: boolean;
  type: string;
  path: string;
  size: number;
  userId: string;
  created: Date;
  url: string;
  description?: string;
}

export enum DocumentSubType {
  Spreadsheet = 'spreadsheet',
  Guidelines = 'guidelines',
  SustainabilityReport = 'sustainability-report',
  AssuranceDocument = 'assurance-document',
  Image = 'image',
  Other = 'other',
}

export type SelectedDocument = Omit<DocumentItem, 'title' | 'description'>;

export enum DocumentMediaType {
  Image = 'image',
  Video = 'video',
  File = 'file',
  Assurance = 'assurance',
  All = 'all',
}

export enum DocumentModalAction {
  Add = 'add',
  Remove = 'remove',
}

export interface DocumentWithAction {
  item: DocumentItem;
  action: DocumentModalAction;
}
