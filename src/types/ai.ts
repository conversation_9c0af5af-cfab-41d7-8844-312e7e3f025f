/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

export interface AIResponse {
  role: 'assistant';
  content: string;
  input?: string;
}

export type MultiRowTableContext = Record<string, string | number | undefined>[];

export interface AdditionalContext {
  inputData?: MultiRowTableContext;
}

export interface AIConfig {
  additionalContext: string;
  aiModel?: AIModel;
}

export enum AIModel {
  GPT_4o = 'gpt-4o',
  Claude_3_5 = 'claude-3.5',
}